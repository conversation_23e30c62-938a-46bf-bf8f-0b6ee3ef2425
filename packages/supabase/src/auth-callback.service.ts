import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';

/**
 * @name createAuthCallbackService
 * @description Factory function to create an instance of `AuthCallbackService`.
 * @param {SupabaseClient} client - Supabase client instance.
 * @returns {AuthCallbackService} - New instance of `AuthCallbackService`.
 */
export function createAuthCallbackService(client: SupabaseClient): AuthCallbackService {
  return new AuthCallbackService(client);
}

/**
 * @name AuthCallbackResult
 * @description Defines the return type for the `exchangeCodeForSession` method.
 */
export interface AuthCallbackResult {
  nextPath: string;
  success: boolean;
  error?: string;
}

/**
 * @name AuthCallbackService
 * @description Handles authentication callbacks, session exchange, and document ownership transfer for Supabase OAuth flows.
 */
class AuthCallbackService {
  /**
   * @param {SupabaseClient} client - Supabase client instance.
   */
  constructor(private readonly client: SupabaseClient) {}

  /**
   * @name exchangeCodeForSession
   * @description Exchanges an auth code for a Supabase session, handles document ownership transfer,
   * and determines the next redirect path.
   *
   * This method performs the following operations:
   * 1. Retrieves the current anonymous session (if any)
   * 2. Exchanges the OAuth code for a new authenticated session
   * 3. Transfers document ownership from anonymous user to authenticated user
   * 4. Cleans up anonymous session data
   *
   * @param {Request} request - The incoming HTTP request containing OAuth callback parameters.
   * @param {Object} params - Additional parameters.
   * @param {string} params.redirectPath - The default redirect path if `redirect_to` is not provided.
   * @returns {Promise<AuthCallbackResult>} - The result containing next path, success status, and optional error.
   */
  async exchangeCodeForSession(
    request: Request,
    { redirectPath }: { redirectPath: string },
  ): Promise<AuthCallbackResult> {
    const requestUrl = new URL(request.url);
    const { searchParams } = requestUrl;
    const next = searchParams.get('next');
    const nextUrl = searchParams.get('redirect_to')?.toString() ?? next ?? redirectPath;
    const authCode = searchParams.get('code');

    if (!authCode) {
      console.warn('No authentication code found in the request URL.');
      return {
        nextPath: nextUrl,
        success: false,
        error: 'No authentication code provided',
      };
    }

    try {
      // 1. Get current anonymous session before OAuth exchange
      const { data: currentSessionData } = await this.client.auth.getSession();
      const anonymousUserId = currentSessionData?.session?.user?.id;

      // 2. Exchange code for new authenticated session
      const { data: exchangeData, error: exchangeError } = await this.client.auth.exchangeCodeForSession(authCode);

      if (exchangeError) {
        console.error('Error exchanging code for session:', {
          error: exchangeError,
          context: 'auth.callback',
        });
        return {
          nextPath: nextUrl,
          success: false,
          error: exchangeError.message,
        };
      }

      // 3. Get the newly authenticated user
      const authenticatedUser = exchangeData?.user;
      const authenticatedUserId = authenticatedUser?.id;

      // 4. Transfer document ownership if both users exist and are different
      if (anonymousUserId && authenticatedUserId && anonymousUserId !== authenticatedUserId) {
        try {
          await this.transferDocumentOwnership(anonymousUserId, authenticatedUserId);
          console.info('Document ownership transfer completed successfully', {
            fromUserId: anonymousUserId,
            toUserId: authenticatedUserId,
            context: 'auth.callback',
          });
        } catch (transferError) {
          console.error('Error during document ownership transfer:', {
            error: transferError,
            fromUserId: anonymousUserId,
            toUserId: authenticatedUserId,
            context: 'auth.callback',
          });
          // Don't fail the entire authentication process for transfer errors
          // The user is still successfully authenticated
        }
      }

      return {
        nextPath: nextUrl,
        success: true,
      };
    } catch (error) {
      console.error('Unexpected error during authentication callback:', {
        error,
        context: 'auth.callback',
      });
      return {
        nextPath: nextUrl,
        success: false,
        error: 'Unexpected error during authentication',
      };
    }
  }

  /**
   * @name transferDocumentOwnership
   * @description Transfers document ownership from anonymous user to authenticated user.
   * @param {string} fromUserId - The anonymous user ID.
   * @param {string} toUserId - The authenticated user ID.
   * @returns {Promise<void>} - Resolves when transfer is complete.
   * @throws {Error} - Throws error if transfer fails.
   * @private
   */
  private async transferDocumentOwnership(fromUserId: string, toUserId: string): Promise<void> {
    try {
      const adminSupabase = getSupabaseServerAdminClient();

      // Check if there are any documents to transfer
      const { data: documentsToTransfer, error: checkError } = await adminSupabase
        .from('documents')
        .select('id')
        .eq('user_id', fromUserId);

      if (checkError) {
        throw new Error(`Error checking documents for transfer: ${checkError.message}`);
      }

      if (documentsToTransfer && documentsToTransfer.length > 0) {
        // Transfer document ownership
        const { error: updateError } = await adminSupabase
          .from('documents')
          .update({ user_id: toUserId })
          .eq('user_id', fromUserId);

        if (updateError) {
          throw new Error(`Error updating document ownership: ${updateError.message}`);
        }

        console.info(`Successfully transferred ${documentsToTransfer.length} documents`, {
          fromUserId,
          toUserId,
          documentCount: documentsToTransfer.length,
          context: 'document.transfer',
        });
      } else {
        console.info('No documents found for transfer', {
          fromUserId,
          toUserId,
          context: 'document.transfer',
        });
      }
    } catch (error) {
      console.error('Document ownership transfer failed:', {
        error,
        fromUserId,
        toUserId,
        context: 'document.transfer',
      });
      throw error;
    }
  }
}

export { AuthCallbackService };
