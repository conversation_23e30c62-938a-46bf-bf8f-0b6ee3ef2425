'use client';

import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { PaymentElement, useStripe, useElements, Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { PaymentFormProps } from '../../types/payment-types';

// Wrapper with Stripe Elements
interface StripeElementsWrapperProps extends PaymentFormProps {
  stripePublicKey: string;
  isSubscription: boolean;
}

// Stripe payment form component
export function StripePaymentForm({
  onSuccess,
  onError,
  clientSecret,
  successUrl,
  cancelUrl,
  isSubscription,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Form submission function
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      if (isSubscription) {
        const { error, setupIntent } = await stripe.confirmSetup({
          elements,
          confirmParams: {
            return_url: successUrl,
          },
          redirect: 'if_required',
        });

        if (error) {
          setErrorMessage(error.message || 'An error occurred during setup');
          onError(new Error(error.message || 'Setup failed'));
          return;
        }

        if (setupIntent && setupIntent.status === 'succeeded') {
          onSuccess(setupIntent.payment_method as string);
        }
      } else {
        // Confirm the payment
        const { error, paymentIntent } = await stripe.confirmPayment({
          elements,
          confirmParams: {
            return_url: successUrl,
          },
          redirect: 'if_required',
        });

        // Handle the payment error
        if (error) {
          setErrorMessage(error.message || 'An error occurred during payment');
          onError(new Error(error.message || 'Payment failed'));
          return;
        }

        // If there is no redirection and the payment is successful
        if (paymentIntent && paymentIntent.status === 'succeeded') {
          onSuccess(paymentIntent.id);
        }
      }
    } catch (error) {
      setErrorMessage('An unexpected error occurredeeee');
      onError(error instanceof Error ? error : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit} className="w-full py-5">
      <PaymentElement
        id="payment-element"
        options={{
          terms: { card: 'never', applePay: 'never' },
          layout: 'tabs',
        }}
      />

      {errorMessage && <div className="text-sm text-red-600 my-2">{errorMessage}</div>}
      <Button
        className={cn(
          'mb-3 mt-4 flex h-14 w-full items-center justify-center gap-1.5 rounded-[10px] bg-[#1FC794] hover:bg-emerald-600',
          'font-onest text-[16px] font-medium leading-5 text-white',
          'desktop:h-12',
          'final:h-12',
        )}
        type="submit"
        disabled={!stripe || !elements || isLoading}
      >
        <Image src={'/images/checkout/Unlock.svg'} alt={'Unlock'} width={20} height={20} className="object-contain" />
        {isLoading ? 'Processing...' : 'Pay and Get My File'}
      </Button>
    </form>
  );
}

export function StripeElementsWrapper({ stripePublicKey, clientSecret, ...props }: StripeElementsWrapperProps) {
  const [stripePromise, setStripePromise] = useState<Promise<any> | null>(null);

  useEffect(() => {
    if (stripePublicKey) {
      setStripePromise(loadStripe(stripePublicKey));
    }
  }, [stripePublicKey]);

  if (!clientSecret || !stripePromise) {
    return <div className="text-center py-4">Loading payment form...</div>;
  }

  // Note: Express payment methods (Apple Pay, Google Pay) are automatically enabled
  // by Stripe if they are configured in the Stripe dashboard and the user's browser/device
  // supports them. There is no need to create custom buttons.
  const options: StripeElementsOptions = {
    clientSecret,
    appearance: {
      theme: 'stripe',
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <StripePaymentForm clientSecret={clientSecret} {...props} />
    </Elements>
  );
}

export default StripeElementsWrapper;
