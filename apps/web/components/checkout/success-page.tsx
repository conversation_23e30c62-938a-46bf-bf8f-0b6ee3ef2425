import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Swal from 'sweetalert2';
import { sendGTMEvent } from '@next/third-parties/google';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { SubscriptionType, usePayment } from '@pdfily/payment';
import { downloadBlob, isBrowser, isGTMInitialized, LocalStorageManager, PostHogEventEnum } from '@pdfily/shared/utils';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useRouter } from '@/lib/i18n/navigation';
import { usePostHogEvents } from '@/lib/tracking/posthog/usePostHogEvents';
import { usePostHogAnalytics } from '@/lib/tracking/posthog/usePostHogAnalytics';

function SuccessPage() {
  const { trackPostHogPurchaseEvent } = usePostHogEvents();
  const { captureEvent } = usePostHogAnalytics();
  const { service } = usePayment();
  const [loading, setLoading] = useState(true);
  const [gtmEventSent, setGtmEventSent] = useState(false);
  const t = useTranslations('Checkout.success');
  const router = useRouter();
  const supabase = useSupabase();
  const { lastEditFileId } = useLastEditFileId();

  useEffect(() => {
    if (!isBrowser || !service || !isGTMInitialized() || gtmEventSent) return;

    const paymentType = LocalStorageManager.getItem('payment-type') as SubscriptionType;

    // Check if GTM event was already sent for this payment
    const gtmEventKey = `gtm-payment-success-sent-${paymentType}`;
    const paymentId = LocalStorageManager.getItem('payment-id');
    const gtmEventAlreadySent = LocalStorageManager.getItem(gtmEventKey);

    if (gtmEventAlreadySent) {
      setGtmEventSent(true);
      return;
    }

    switch (paymentType) {
      case SubscriptionType.WEEKLY: {
        if (!paymentId) return;

        service.verifyPayment(paymentId).then((payment) => {
          if (payment?.isValid) {
            sendGTMEvent({ event: 'payment_success', period: '4-weeks', payment: payment.details });
            trackPostHogPurchaseEvent({ ...payment.details, isTrial: true, SubscriptionType: SubscriptionType.WEEKLY });

            // Mark GTM event as sent in localStorage
            LocalStorageManager.setItem(gtmEventKey, 'true');
            setGtmEventSent(true);
          }
        });
        break;
      }

      case SubscriptionType.ANNUAL: {
        const subscriptionStatus = LocalStorageManager.getItem('subscription-status');
        if (subscriptionStatus === 'active') {
          sendGTMEvent({ event: 'payment_success', period: 'Annual' });
          trackPostHogPurchaseEvent({ isTrial: true, SubscriptionType: SubscriptionType.ANNUAL });

          // Mark GTM event as sent in localStorage
          LocalStorageManager.setItem(gtmEventKey, 'true');
          setGtmEventSent(true);
        }
        break;
      }

      default:
        // No action for other types
        break;
    }
  }, [service, trackPostHogPurchaseEvent, gtmEventSent]);

  useEffect(() => {
    const footer = document.querySelector<HTMLElement>('footer');
    if (footer) footer.style.display = 'none';

    const timer = setTimeout(() => {
      setLoading(false);
    }, 3000);

    return () => {
      if (footer) footer.style.display = 'flex';
      clearTimeout(timer);
    };
  }, []);

  const handleDownload = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      const { data } = await supabase.rpc('has_subscription_status', {
        user_id: user.id,
      });

      const subscription = data?.[0];
      const hasSubscription = (subscription?.has_subscription || user.user_metadata.is_subscribed) ?? false;

      if (!hasSubscription) {
        router.push('/checkout?step=payment');
        return;
      }

      // Retrieve the document ID from URL or state
      const documentId = new URLSearchParams(window.location.search).get('documentId') || lastEditFileId;

      if (!documentId) {
        Swal.fire({
          icon: 'error',
          title: 'Erreur',
          text: 'Document non trouvé',
        });
        return;
      }

      const response = await fetch(`/api/v1/files/${encodeURIComponent(documentId)}/download`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        const { error, message } = await response.json();
        Swal.fire({
          icon: 'error',
          title: error ?? 'Oops...',
          text: message ?? 'An error occurred while downloading the file.',
        });
        return;
      }

      const blob = await response.blob();
      downloadBlob(blob, response, '');
      captureEvent(PostHogEventEnum.DOWNLOAD_FILE, { documentId });
    } catch (error) {
      console.log({ error });
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'An error occurred while downloading.',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-[60vh] flex-col items-center justify-center text-center">
        <div className="mb-6 h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-[#1FC794]" />
        <p className="text-lg font-medium text-[#585858]">Preparing your file…</p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'my-[148px] flex w-[400px] flex-col items-center',
        'small:mt-10 small:w-full',
        'final:mt-10 final:w-full',
      )}
    >
      <Image
        src="/images/checkout/CircleCheck_outline.svg"
        alt="Check"
        width={132}
        height={132}
        className={cn('mb-10 object-contain', 'small:mb-5 small:h-16 small:w-16', 'final:mb-5 final:h-16 final:w-16')}
      />

      <h2
        className={cn(
          'mb-5 font-onest text-[32px] font-medium leading-9 text-[#1C1C1C]',
          'small:mb-[10px] small:text-2xl small:leading-7',
          'final:mb-[10px] final:text-2xl final:leading-7',
        )}
      >
        {t('title')}
      </h2>

      <p
        className={cn(
          'mb-8 text-center font-onest text-lg font-normal leading-[26px] text-[#585858]',
          'small:mb-6 small:text-[16px] small:leading-6',
          'final:mb-6 final:text-[16px] final:leading-6',
        )}
      >
        {t.rich('downloadText', {
          br: () => <br />,
        })}
      </p>

      <Button
        onClick={handleDownload}
        className={cn(
          'h-14 w-full rounded-[10px] bg-[#1FC794] font-onest text-[16px] font-medium leading-5 text-white hover:bg-emerald-600',
          'small:h-12',
          'final:h-12',
        )}
      >
        {t('downloadButton')}
      </Button>
    </div>
  );
}

export default SuccessPage;
