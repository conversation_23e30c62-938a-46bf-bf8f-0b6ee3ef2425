'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ComponentProps, useState } from 'react';
import { cn } from '@pdfily/ui/utils';
import authConfig from '@pdfily/config/auth.config';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';

import Swal from 'sweetalert2';

import { Button } from '../ui/button';

/**
 * Props interface for the GoogleAuthButton component.
 */
type GoogleAuthButtonProps = ComponentProps<typeof Button> & {
  className?: string;
  path: string;
};

/**
 * GoogleAuthButton component
 */
export default function GoogleAuthButton({ path, className = '', ...rest }: GoogleAuthButtonProps) {
  const t = useTranslations('Auth');
  const { googleAuthEnabled } = authConfig;
  const [isLoading, setIsLoading] = useState(false);

  const supabase = useSupabase();

  const handleGoogleSignIn = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Use Supabase's approach for popup authentication
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          skipBrowserRedirect: true,
          queryParams: { prompt: 'select_account' },
          redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(`/auth/popup-success?redirect_to=${encodeURIComponent(path)}`)}`,
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.url) {
        throw new Error('No authentication URL received');
      }

      // Open popup with the OAuth URL
      const popup = openPopup(data.url);

      if (!popup) {
        throw new Error('Popup was blocked');
      }

      // Listen for the popup to complete authentication
      let authCompleted = false; // Flag to track if auth completed successfully

      const checkClosed = setInterval(() => {
        try {
          // Check if popup has finished redirecting (is on the popup success page)
          if (!popup.closed && popup.location && popup.location.pathname.includes('/auth/popup-success')) {
            // Popup has finished redirecting, check authentication
            supabase.auth.getSession().then(({ data: sessionData }) => {
              if (sessionData.session) {
                console.log('Authentication successful, popup will handle redirection');
                authCompleted = true; // Mark as completed successfully
                clearInterval(checkClosed);
                setIsLoading(false);
                // Don't redirect here - the popup page will handle redirecting the parent window
                // and closing itself
                return;
              }
            });
          }
        } catch {
          // Cross-origin error means popup is still on Google/auth domain, continue waiting
        }

        // Check if popup was closed manually
        if (popup.closed) {
          clearInterval(checkClosed);
          setIsLoading(false);

          // If authentication was completed successfully, the popup page already handled redirection
          // If popup was closed manually, don't redirect
          if (authCompleted) {
            console.log('Authentication completed - popup handled redirection');
          } else {
            console.log('Popup was closed manually - no redirection');
          }
        }
      }, 1000);
    } catch (error) {
      console.error('Google sign-in error:', error);
      setIsLoading(false);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to sign in with Google. Please try again.',
      });
    }
  };

  const openPopup = (url: string) => {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    const windowFeatures = `scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`;
    const popup = window.open(url, 'google-auth-popup', windowFeatures);
    return popup;
  };

  return (
    <>
      {googleAuthEnabled ? (
        <div>
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className={cn(
              'flex gap-x-2 justify-center items-center p-3 border w-full border-gray-200 hover:bg-gray-50 hover:shadow-md hover:border-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
              className,
            )}
            {...rest}
          >
            <Image src="/images/auth/google.svg" alt="google" width={500} height={28} className="w-7 h-7" />
            <p>
              {t('loginWithGoogle')} {isLoading ? '...' : ''}
            </p>
          </button>
          <div className="flex items-center my-4">
            <div className="flex-grow h-px bg-gray-300" />
            <span className="px-4 text-sm text-gray-500">OR</span>
            <div className="flex-grow h-px bg-gray-300" />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
}
