'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * Page shown in popup after successful Google authentication
 * This page stays blank and handles redirecting the parent window
 */
export default function PopupSuccess() {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get the redirect path from URL params
    const redirectTo = searchParams.get('redirect_to') || '/';

    // Small delay to ensure everything is loaded
    const timer = setTimeout(() => {
      try {
        // Redirect the parent window (opener) to the desired path
        if (window.opener && !window.opener.closed) {
          window.opener.location.href = redirectTo;
        }

        // Close this popup
        window.close();
      } catch (error) {
        console.error('Error redirecting parent window:', error);
        // Fallback: just close the popup
        window.close();
      }
    }, 500); // Small delay to ensure smooth UX

    return () => clearTimeout(timer);
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-300 mx-auto mb-4"></div>
      </div>
    </div>
  );
}
