import { ReactNode } from 'react';

/**
 * Layout for popup-success page - completely bare with no styling or components
 * This ensures the popup page loads as fast as possible with minimal overhead
 */
export default function PopupSuccessLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html>
      <head>
        <title>Authentication Success</title>
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
